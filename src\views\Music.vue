<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">音乐作品</h1>
        <p class="text-xl text-gray-600">探索泰勒·斯威夫特的音乐世界</p>
      </div>

      <!-- 热门歌曲 -->
      <div class="mb-12">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
          <i class="fas fa-fire text-red-500 mr-3"></i>
          热门歌曲
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="song in featuredSongs"
            :key="song.id"
            class="song-card"
            @click="playSong(song)"
          >
            <div class="relative">
              <img
                :src="song.cover"
                :alt="song.title"
                class="w-full h-48 object-cover rounded-lg"
              />
              <div class="absolute inset-0 bg-black bg-opacity-40 rounded-lg flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-200">
                <button class="bg-white text-primary-600 w-16 h-16 rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-200">
                  <i class="fas fa-play text-xl ml-1"></i>
                </button>
              </div>
              <div v-if="song.isPopular" class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                热门
              </div>
            </div>
            <div class="mt-4">
              <h3 class="font-semibold text-gray-900 truncate">{{ song.title }}</h3>
              <p class="text-gray-600 text-sm">{{ song.album }}</p>
              <p class="text-gray-500 text-xs">{{ song.duration }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 专辑列表 -->
      <div>
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
          <i class="fas fa-compact-disc text-primary-500 mr-3"></i>
          专辑作品
        </h2>
        <div class="space-y-8">
          <div
            v-for="album in musicAlbums"
            :key="album.id"
            class="album-card"
          >
            <div class="md:flex">
              <!-- 专辑封面 -->
              <div class="md:w-1/4 p-6">
                <img
                  :src="album.cover"
                  :alt="album.title"
                  class="w-full max-w-xs mx-auto rounded-xl shadow-lg"
                />
              </div>

              <!-- 专辑信息和歌曲列表 -->
              <div class="md:w-3/4 p-6">
                <div class="mb-6">
                  <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ album.title }}</h3>
                  <div class="flex items-center space-x-4 text-gray-600 mb-3">
                    <span class="flex items-center">
                      <i class="fas fa-calendar mr-2"></i>
                      {{ album.year }}
                    </span>
                    <span class="flex items-center">
                      <i class="fas fa-music mr-2"></i>
                      {{ album.genre }}
                    </span>
                    <span class="flex items-center">
                      <i class="fas fa-list mr-2"></i>
                      {{ album.tracks.length }} 首歌曲
                    </span>
                  </div>
                  <p class="text-gray-700">{{ album.description }}</p>
                </div>

                <!-- 歌曲列表 -->
                <div class="space-y-2">
                  <div
                    v-for="(track, index) in album.tracks"
                    :key="track.id"
                    class="track-item"
                    @click="playSong(track, album.tracks)"
                  >
                    <div class="flex items-center">
                      <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-4 text-sm text-gray-600">
                        {{ index + 1 }}
                      </div>
                      <div class="flex-1 min-w-0">
                        <h4 class="font-medium text-gray-900 truncate">{{ track.title }}</h4>
                      </div>
                      <div class="text-gray-500 text-sm mr-4">{{ track.duration }}</div>
                      <button class="text-primary-600 hover:text-primary-700 transition-colors">
                        <i class="fas fa-play"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 音乐播放器 -->
    <MusicPlayer ref="musicPlayer" :playlist="currentPlaylist" />
  </div>
</template>

<script>
import MusicPlayer from '@/components/MusicPlayer.vue'
import { musicAlbums, featuredSongs } from '@/data/musicData.js'

export default {
  name: 'Music',
  components: {
    MusicPlayer
  },
  data() {
    return {
      musicAlbums,
      featuredSongs,
      currentPlaylist: []
    }
  },
  methods: {
    playSong(song, playlist = null) {
      if (playlist) {
        this.currentPlaylist = playlist
        const index = playlist.findIndex(track => track.id === song.id)
        this.$refs.musicPlayer.playSong(song, index)
      } else {
        this.currentPlaylist = [song]
        this.$refs.musicPlayer.playSong(song, 0)
      }
    }
  }
}
</script>

<style scoped>
.song-card {
  @apply bg-white rounded-xl shadow-lg overflow-hidden cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl;
}

.album-card {
  @apply bg-white rounded-xl shadow-lg overflow-hidden;
}

.track-item {
  @apply p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors duration-200;
}

.track-item:hover .fa-play {
  @apply text-primary-700;
}
</style>
