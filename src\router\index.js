import Vue from 'vue'
import VueRouter from 'vue-router'
import Biography from '../views/Biography.vue'
import Music from '../views/Music.vue'
import Movies from '../views/Movies.vue'
import News from '../views/News.vue'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    redirect: '/biography'
  },
  {
    path: '/biography',
    name: 'Biography',
    component: Biography,
    meta: {
      title: '明星简介'
    }
  },
  {
    path: '/music',
    name: 'Music',
    component: Music,
    meta: {
      title: '音乐作品'
    }
  },
  {
    path: '/movies',
    name: 'Movies',
    component: Movies,
    meta: {
      title: '影视作品'
    }
  },
  {
    path: '/news',
    name: 'News',
    component: News,
    meta: {
      title: '娱乐新闻'
    }
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { x: 0, y: 0 }
    }
  }
})

// 路由守卫，设置页面标题
router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = `${to.meta.title} - 明星个人信息`
  }
  next()
})

export default router
