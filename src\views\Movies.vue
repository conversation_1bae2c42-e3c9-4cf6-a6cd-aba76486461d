<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">影视作品</h1>
        <p class="text-xl text-gray-600">泰勒·斯威夫特的银幕之旅</p>
      </div>

      <!-- 分类导航 -->
      <div class="flex justify-center mb-8">
        <div class="bg-white rounded-lg p-1 shadow-lg">
          <button
            v-for="category in categories"
            :key="category.id"
            @click="activeCategory = category.id"
            class="category-btn"
            :class="{ 'category-btn-active': activeCategory === category.id }"
          >
            <i :class="category.icon" class="mr-2"></i>
            {{ category.name }}
          </button>
        </div>
      </div>

      <!-- 电影作品 -->
      <div v-if="activeCategory === 'movies'" class="mb-12">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
          <i class="fas fa-film text-primary-500 mr-3"></i>
          电影作品
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <div
            v-for="movie in movies"
            :key="movie.id"
            class="movie-card"
            @click="selectedMovie = movie"
          >
            <div class="relative">
              <img
                :src="movie.poster"
                :alt="movie.title"
                class="w-full h-80 object-cover rounded-lg"
              />
              <div class="absolute top-2 left-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
                {{ movie.type }}
              </div>
              <div class="absolute top-2 right-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold">
                {{ movie.rating }}
              </div>
            </div>
            <div class="mt-4">
              <h3 class="font-bold text-gray-900 truncate">{{ movie.title }}</h3>
              <p class="text-gray-600 text-sm">{{ movie.year }}</p>
              <p class="text-gray-500 text-xs">{{ movie.duration }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 音乐录影带 -->
      <div v-if="activeCategory === 'mvs'" class="mb-12">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
          <i class="fas fa-video text-primary-500 mr-3"></i>
          音乐录影带
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="mv in musicVideos"
            :key="mv.id"
            class="mv-card"
          >
            <div class="relative">
              <img
                :src="mv.thumbnail"
                :alt="mv.title"
                class="w-full h-48 object-cover rounded-lg"
              />
              <div class="absolute inset-0 bg-black bg-opacity-40 rounded-lg flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-200">
                <button class="bg-white text-red-600 w-16 h-16 rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-200">
                  <i class="fas fa-play text-xl ml-1"></i>
                </button>
              </div>
              <div class="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
                {{ mv.duration }}
              </div>
            </div>
            <div class="mt-4">
              <h3 class="font-bold text-gray-900 truncate">{{ mv.title }}</h3>
              <p class="text-gray-600 text-sm">{{ mv.album }} · {{ mv.year }}</p>
              <p class="text-gray-500 text-xs">{{ mv.views }} 次观看</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 电视节目 -->
      <div v-if="activeCategory === 'tv'" class="mb-12">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
          <i class="fas fa-tv text-primary-500 mr-3"></i>
          电视节目
        </h2>
        <div class="space-y-6">
          <div
            v-for="appearance in tvAppearances"
            :key="appearance.id"
            class="tv-card"
          >
            <div class="md:flex">
              <div class="md:w-1/3">
                <img
                  :src="appearance.image"
                  :alt="appearance.show"
                  class="w-full h-48 md:h-full object-cover rounded-lg"
                />
              </div>
              <div class="md:w-2/3 p-6">
                <h3 class="text-xl font-bold text-gray-900 mb-2">{{ appearance.show }}</h3>
                <div class="flex items-center space-x-4 text-gray-600 mb-3">
                  <span class="flex items-center">
                    <i class="fas fa-calendar mr-2"></i>
                    {{ appearance.date }}
                  </span>
                  <span class="flex items-center">
                    <i class="fas fa-user mr-2"></i>
                    {{ appearance.role }}
                  </span>
                </div>
                <p class="text-gray-700">{{ appearance.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 电影详情模态框 -->
    <div
      v-if="selectedMovie"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      @click="selectedMovie = null"
    >
      <div
        class="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        @click.stop
      >
        <div class="relative">
          <img
            :src="selectedMovie.poster"
            :alt="selectedMovie.title"
            class="w-full h-64 object-cover"
          />
          <button
            @click="selectedMovie = null"
            class="absolute top-4 right-4 bg-black bg-opacity-50 text-white w-10 h-10 rounded-full flex items-center justify-center hover:bg-opacity-70 transition-colors"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="p-8">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">{{ selectedMovie.title }}</h2>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div>
              <span class="text-gray-500 text-sm">年份</span>
              <p class="font-medium">{{ selectedMovie.year }}</p>
            </div>
            <div>
              <span class="text-gray-500 text-sm">类型</span>
              <p class="font-medium">{{ selectedMovie.type }}</p>
            </div>
            <div>
              <span class="text-gray-500 text-sm">时长</span>
              <p class="font-medium">{{ selectedMovie.duration }}</p>
            </div>
            <div>
              <span class="text-gray-500 text-sm">评分</span>
              <p class="font-medium">{{ selectedMovie.rating }}</p>
            </div>
          </div>
          <p class="text-gray-700 mb-6">{{ selectedMovie.description }}</p>
          <div v-if="selectedMovie.awards" class="mb-4">
            <h4 class="font-semibold text-gray-900 mb-2">获奖情况</h4>
            <div class="flex flex-wrap gap-2">
              <span
                v-for="award in selectedMovie.awards"
                :key="award"
                class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm"
              >
                {{ award }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { movies, musicVideos, tvAppearances } from '@/data/moviesData.js'

export default {
  name: 'Movies',
  data() {
    return {
      movies,
      musicVideos,
      tvAppearances,
      activeCategory: 'movies',
      selectedMovie: null,
      categories: [
        { id: 'movies', name: '电影', icon: 'fas fa-film' },
        { id: 'mvs', name: '音乐录影带', icon: 'fas fa-video' },
        { id: 'tv', name: '电视节目', icon: 'fas fa-tv' }
      ]
    }
  }
}
</script>

<style scoped>
.category-btn {
  @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 text-gray-600 hover:text-primary-600;
}

.category-btn-active {
  @apply bg-primary-500 text-white shadow-lg;
}

.movie-card {
  @apply bg-white rounded-xl shadow-lg overflow-hidden cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl;
}

.mv-card {
  @apply bg-white rounded-xl shadow-lg overflow-hidden cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl;
}

.tv-card {
  @apply bg-white rounded-xl shadow-lg overflow-hidden;
}
</style>
