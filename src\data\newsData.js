export const news = [
  {
    id: 1,
    title: '泰勒·斯威夫特《The Eras Tour》演唱会电影创下票房新纪录',
    summary: '这部演唱会电影在全球范围内获得了巨大成功，成为史上最成功的演唱会电影之一。',
    content: `
      泰勒·斯威夫特的《The Eras Tour》演唱会电影自上映以来就创下了多项票房纪录。这部电影记录了她史诗级巡回演唱会的精彩瞬间，涵盖了她职业生涯各个时期的经典歌曲。
      
      影片在首映周末就获得了超过9200万美元的票房收入，成为史上开画票房最高的演唱会电影。全球票房更是突破了2.6亿美元大关，证明了泰勒在全球范围内的巨大影响力。
      
      这部电影不仅是一场视听盛宴，更是泰勒音乐生涯的完整回顾。从早期的乡村音乐到后来的流行音乐，再到近期的民谣风格，每一个"时代"都在电影中得到了完美呈现。
    `,
    date: '2023年11月15日',
    category: '电影',
    image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=600&h=400&fit=crop',
    author: '娱乐记者 张小明',
    tags: ['演唱会', '电影', '票房', 'The Eras Tour']
  },
  {
    id: 2,
    title: '泰勒·斯威夫特荣获《时代》杂志2023年度人物',
    summary: '《时代》杂志宣布泰勒·斯威夫特为2023年度人物，表彰她在音乐和文化领域的巨大影响力。',
    content: `
      《时代》杂志正式宣布泰勒·斯威夫特为2023年度人物，这是对她在过去一年中取得的非凡成就的认可。杂志编辑部表示，泰勒不仅在音乐领域取得了巨大成功，更在社会文化层面产生了深远影响。
      
      2023年对泰勒来说是具有里程碑意义的一年。她的《The Eras Tour》巡回演唱会成为史上最成功的巡演之一，不仅创下了票房纪录，还对各个城市的经济产生了显著的积极影响。
      
      此外，她重新录制早期专辑的举动也引发了关于艺术家权益和音乐版权的重要讨论。泰勒通过自己的行动，为整个音乐行业的艺术家权益保护树立了榜样。
    `,
    date: '2023年12月06日',
    category: '荣誉',
    image: 'https://images.unsplash.com/photo-1516280440614-37939bbacd81?w=600&h=400&fit=crop',
    author: '时代杂志编辑部',
    tags: ['年度人物', '时代杂志', '社会影响', '音乐']
  },
  {
    id: 3,
    title: '泰勒·斯威夫特宣布重新录制《1989》专辑',
    summary: '继《Fearless》和《Red》之后，泰勒宣布将重新录制她的经典专辑《1989》。',
    content: `
      泰勒·斯威夫特在社交媒体上正式宣布，她将重新录制2014年的经典专辑《1989》。这是她"Taylor's Version"项目的重要组成部分，旨在重新获得自己早期作品的版权控制。
      
      《1989》是泰勒从乡村音乐转向流行音乐的标志性专辑，包含了《Shake It Off》、《Blank Space》等多首热门单曲。重新录制版本将保持原有歌曲的精神内核，同时融入泰勒现在更加成熟的音乐理解。
      
      这一举动不仅是对自己音乐遗产的保护，也为其他艺术家争取音乐版权提供了重要的参考案例。泰勒的粉丝们对此表示强烈支持，期待听到这些经典歌曲的全新演绎。
    `,
    date: '2023年10月27日',
    category: '音乐',
    image: 'https://images.unsplash.com/photo-1524368535928-5b5e00ddc76b?w=600&h=400&fit=crop',
    author: '音乐记者 李小红',
    tags: ['1989', 'Taylor\'s Version', '重新录制', '版权']
  },
  {
    id: 4,
    title: '泰勒·斯威夫特慈善捐款支持教育事业',
    summary: '泰勒向多个教育机构捐款，支持音乐教育和奖学金项目。',
    content: `
      泰勒·斯威夫特近日向全美多个教育机构捐赠了总计500万美元，用于支持音乐教育项目和为有需要的学生提供奖学金。这一慈善举动体现了她对教育事业的长期关注和支持。
      
      捐款将主要用于购买乐器、建设音乐教室以及为有音乐天赋但经济困难的学生提供学习机会。泰勒表示，音乐教育对年轻人的成长具有重要意义，她希望通过自己的努力让更多孩子能够接触到音乐。
      
      这并不是泰勒第一次参与慈善活动。多年来，她一直积极支持各种社会公益事业，包括灾难救助、LGBTQ+权益保护、女性权益等。她的慈善行为赢得了社会各界的广泛赞誉。
    `,
    date: '2023年09月15日',
    category: '慈善',
    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop',
    author: '公益记者 王小华',
    tags: ['慈善', '教育', '音乐教育', '奖学金']
  },
  {
    id: 5,
    title: '《Midnights》专辑获得格莱美奖多项提名',
    summary: '泰勒的最新专辑《Midnights》获得了第66届格莱美奖的多项重要提名。',
    content: `
      第66届格莱美奖提名名单公布，泰勒·斯威夫特的专辑《Midnights》获得了包括年度专辑、年度歌曲在内的多项重要提名。这标志着她继续在音乐界保持着顶级的创作水准。
      
      《Midnights》自发行以来就获得了巨大的商业成功和评论界的好评。专辑中的歌曲《Anti-Hero》更是成为了全球热门单曲，在各大音乐榜单上都取得了优异成绩。
      
      如果泰勒能够在本届格莱美奖上获奖，她将进一步巩固自己在音乐史上的地位。她已经是格莱美奖历史上获奖次数最多的艺术家之一，这次提名再次证明了她持续的创作活力。
    `,
    date: '2023年11月10日',
    category: '奖项',
    image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=600&h=400&fit=crop',
    author: '音乐评论家 赵小刚',
    tags: ['格莱美奖', 'Midnights', '提名', '音乐奖项']
  },
  {
    id: 6,
    title: '泰勒·斯威夫特与Travis Kelce恋情公开',
    summary: '泰勒与NFL球星Travis Kelce的恋情成为娱乐圈热门话题。',
    content: `
      泰勒·斯威夫特与堪萨斯城酋长队明星球员Travis Kelce的恋情正式公开，两人多次被拍到一起观看NFL比赛和约会。这段跨界恋情引起了音乐界和体育界的广泛关注。
      
      Travis Kelce是NFL的顶级球员，曾多次入选职业碗并获得超级碗冠军。他在播客节目中公开表达了对泰勒的欣赏，随后两人开始交往。泰勒也多次现身酋长队的比赛现场为男友加油。
      
      这段恋情不仅让两人的粉丝群体产生了有趣的交集，也为NFL带来了更多年轻女性观众。媒体和粉丝都对这对情侣的未来发展充满期待。
    `,
    date: '2023年10月01日',
    category: '感情',
    image: 'https://images.unsplash.com/photo-1516280440614-37939bbacd81?w=600&h=400&fit=crop',
    author: '娱乐记者 陈小美',
    tags: ['恋情', 'Travis Kelce', 'NFL', '感情生活']
  }
]

export const newsCategories = [
  { id: 'all', name: '全部', icon: 'fas fa-globe' },
  { id: '音乐', name: '音乐', icon: 'fas fa-music' },
  { id: '电影', name: '电影', icon: 'fas fa-film' },
  { id: '荣誉', name: '荣誉', icon: 'fas fa-trophy' },
  { id: '慈善', name: '慈善', icon: 'fas fa-heart' },
  { id: '奖项', name: '奖项', icon: 'fas fa-award' },
  { id: '感情', name: '感情', icon: 'fas fa-heart' }
]
