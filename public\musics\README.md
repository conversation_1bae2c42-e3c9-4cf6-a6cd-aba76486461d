# 音乐资源目录

请将MP3音乐文件放置在此目录下。

## 当前需要的文件
根据 `src/data/musicData.js` 配置，需要以下文件：

```
public/musics/
├── 最好的我们.mp3          # 《最好的我们》主题曲
├── 开端片尾曲.mp3          # 《开端》片尾曲
├── 沉默的真相插曲.mp3      # 《沉默的真相》插曲
└── 卿卿日常主题曲.mp3      # 《卿卿日常》主题曲
```

## 使用说明

1. 将MP3文件放入此目录，文件名必须与上述列表完全一致
2. 在代码中使用绝对路径访问：`/musics/文件名.mp3`
3. 例如：`/musics/最好的我们.mp3`

## 测试方法

1. 将MP3文件放入此目录后
2. 打开浏览器开发者工具（F12）
3. 点击播放音乐，查看控制台日志
4. 如果出现错误，会显示具体的错误信息和文件路径

## 注意事项

- **文件名必须完全匹配**，包括中文字符
- 推荐音频格式：MP3, 比特率：128kbps-320kbps
- 文件大小建议控制在10MB以内
- 确保音频文件没有版权问题
- 如果使用中文文件名出现问题，可以修改 `src/data/musicData.js` 中的文件名为英文

## 故障排除

如果音乐无法播放：

1. 检查文件是否存在于 `public/musics/` 目录
2. 检查文件名是否完全匹配（区分大小写）
3. 检查浏览器控制台的错误信息
4. 尝试直接访问 `http://localhost:8083/musics/文件名.mp3` 看是否能下载
5. 确保MP3文件格式正确且未损坏
