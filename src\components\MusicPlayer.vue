<template>
  <div v-if="currentSong" class="fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t z-40">
    <div class="max-w-7xl mx-auto px-4 py-3">
      <div class="flex items-center justify-between">
        <!-- 歌曲信息 -->
        <div class="flex items-center space-x-4 flex-1 min-w-0">
          <img
            :src="currentSong.cover"
            :alt="currentSong.title"
            class="w-12 h-12 rounded-lg object-cover"
          />
          <div class="min-w-0 flex-1">
            <h4 class="text-sm font-medium text-gray-900 truncate">{{ currentSong.title }}</h4>
            <p class="text-xs text-gray-500 truncate">{{ currentSong.artist }}</p>
          </div>
        </div>

        <!-- 播放控制 -->
        <div class="flex items-center space-x-4">
          <button
            @click="previousSong"
            class="text-gray-600 hover:text-primary-600 transition-colors"
            :disabled="!hasPrevious"
          >
            <i class="fas fa-step-backward"></i>
          </button>

          <button
            @click="togglePlay"
            class="bg-primary-500 hover:bg-primary-600 text-white w-10 h-10 rounded-full flex items-center justify-center transition-colors"
          >
            <i :class="isPlaying ? 'fas fa-pause' : 'fas fa-play'" class="text-sm"></i>
          </button>

          <button
            @click="nextSong"
            class="text-gray-600 hover:text-primary-600 transition-colors"
            :disabled="!hasNext"
          >
            <i class="fas fa-step-forward"></i>
          </button>
        </div>

        <!-- 进度条和时间 -->
        <div class="hidden md:flex items-center space-x-3 flex-1 max-w-md">
          <span class="text-xs text-gray-500 w-10">{{ formatTime(currentTime) }}</span>
          <div class="flex-1 relative">
            <div class="h-1 bg-gray-200 rounded-full">
              <div
                class="h-1 bg-primary-500 rounded-full transition-all duration-100"
                :style="{ width: progressPercentage + '%' }"
              ></div>
            </div>
            <input
              type="range"
              min="0"
              :max="duration"
              v-model="currentTime"
              @input="seek"
              class="absolute inset-0 w-full h-1 opacity-0 cursor-pointer"
            />
          </div>
          <span class="text-xs text-gray-500 w-10">{{ formatTime(duration) }}</span>
        </div>

        <!-- 音量控制 -->
        <div class="hidden lg:flex items-center space-x-2">
          <button @click="toggleMute" class="text-gray-600 hover:text-primary-600 transition-colors">
            <i :class="volumeIcon" class="text-sm"></i>
          </button>
          <div class="w-20 relative">
            <div class="h-1 bg-gray-200 rounded-full">
              <div
                class="h-1 bg-primary-500 rounded-full"
                :style="{ width: volume * 100 + '%' }"
              ></div>
            </div>
            <input
              type="range"
              min="0"
              max="1"
              step="0.01"
              v-model="volume"
              @input="setVolume"
              class="absolute inset-0 w-full h-1 opacity-0 cursor-pointer"
            />
          </div>
        </div>

        <!-- 关闭按钮 -->
        <button
          @click="closePlayers"
          class="ml-4 text-gray-600 hover:text-red-600 transition-colors"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- 移动端进度条 -->
      <div class="md:hidden mt-2">
        <div class="flex items-center space-x-2">
          <span class="text-xs text-gray-500">{{ formatTime(currentTime) }}</span>
          <div class="flex-1 relative">
            <div class="h-1 bg-gray-200 rounded-full">
              <div
                class="h-1 bg-primary-500 rounded-full transition-all duration-100"
                :style="{ width: progressPercentage + '%' }"
              ></div>
            </div>
            <input
              type="range"
              min="0"
              :max="duration"
              v-model="currentTime"
              @input="seek"
              class="absolute inset-0 w-full h-1 opacity-0 cursor-pointer"
            />
          </div>
          <span class="text-xs text-gray-500">{{ formatTime(duration) }}</span>
        </div>
      </div>
    </div>

    <!-- 音频元素 -->
    <audio
      ref="audio"
      @loadedmetadata="onLoadedMetadata"
      @timeupdate="onTimeUpdate"
      @ended="onEnded"
      @play="onPlay"
      @pause="onPause"
      @error="onError"
      @loadstart="onLoadStart"
      @canplay="onCanPlay"
    ></audio>
  </div>
</template>

<script>
export default {
  name: 'MusicPlayer',
  props: {
    playlist: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentSong: null,
      currentIndex: -1,
      isPlaying: false,
      currentTime: 0,
      duration: 0,
      volume: 0.7,
      isMuted: false,
      previousVolume: 0.7
    }
  },
  computed: {
    progressPercentage() {
      return this.duration > 0 ? (this.currentTime / this.duration) * 100 : 0
    },
    hasPrevious() {
      return this.currentIndex > 0
    },
    hasNext() {
      return this.currentIndex < this.playlist.length - 1
    },
    volumeIcon() {
      if (this.isMuted || this.volume === 0) {
        return 'fas fa-volume-mute'
      } else if (this.volume < 0.5) {
        return 'fas fa-volume-down'
      } else {
        return 'fas fa-volume-up'
      }
    }
  },
  methods: {
    playSong(song, index = -1) {
      console.log('尝试播放歌曲:', song.title, '路径:', song.audioUrl)
      this.currentSong = song
      this.currentIndex = index
      this.$refs.audio.src = song.audioUrl
      this.$refs.audio.load()
      this.play()
    },
    play() {
      this.$refs.audio.play()
    },
    pause() {
      this.$refs.audio.pause()
    },
    togglePlay() {
      if (this.isPlaying) {
        this.pause()
      } else {
        this.play()
      }
    },
    previousSong() {
      if (this.hasPrevious) {
        const prevIndex = this.currentIndex - 1
        this.playSong(this.playlist[prevIndex], prevIndex)
      }
    },
    nextSong() {
      if (this.hasNext) {
        const nextIndex = this.currentIndex + 1
        this.playSong(this.playlist[nextIndex], nextIndex)
      }
    },
    seek() {
      this.$refs.audio.currentTime = this.currentTime
    },
    setVolume() {
      this.$refs.audio.volume = this.volume
      this.isMuted = this.volume === 0
    },
    toggleMute() {
      if (this.isMuted) {
        this.volume = this.previousVolume
        this.isMuted = false
      } else {
        this.previousVolume = this.volume
        this.volume = 0
        this.isMuted = true
      }
      this.setVolume()
    },
    closePlayers() {
      this.pause()
      this.currentSong = null
      this.currentIndex = -1
    },
    formatTime(seconds) {
      const mins = Math.floor(seconds / 60)
      const secs = Math.floor(seconds % 60)
      return `${mins}:${secs.toString().padStart(2, '0')}`
    },
    onLoadedMetadata() {
      this.duration = this.$refs.audio.duration
      this.setVolume()
    },
    onTimeUpdate() {
      this.currentTime = this.$refs.audio.currentTime
    },
    onEnded() {
      if (this.hasNext) {
        this.nextSong()
      } else {
        this.isPlaying = false
      }
    },
    onPlay() {
      this.isPlaying = true
    },
    onPause() {
      this.isPlaying = false
    },
    onError(event) {
      console.error('音频加载错误:', event)
      console.error('错误详情:', this.$refs.audio.error)
      console.error('当前音频路径:', this.$refs.audio.src)
      alert(`音频加载失败: ${this.currentSong?.title}\n请检查文件路径: ${this.$refs.audio.src}`)
    },
    onLoadStart() {
      console.log('开始加载音频:', this.$refs.audio.src)
    },
    onCanPlay() {
      console.log('音频可以播放:', this.$refs.audio.src)
    }
  }
}
</script>

<style scoped>
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
