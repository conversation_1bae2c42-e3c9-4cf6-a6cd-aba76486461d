<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端API测试</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>前端API测试</h1>
    <button onclick="testAPI()">测试新闻API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在加载...';
            
            try {
                console.log('开始测试前端API调用...');
                
                const response = await axios.get('http://localhost:3001/api/news', {
                    params: {
                        num: '5',
                        page: '1',
                        word: '白敬亭'
                    }
                });
                
                console.log('API响应:', response.data);
                
                if (response.data.success && response.data.data) {
                    resultDiv.innerHTML = `
                        <h2>✅ API调用成功!</h2>
                        <p>获取到 ${response.data.data.length} 条新闻</p>
                        <h3>第一条新闻:</h3>
                        <p><strong>标题:</strong> ${response.data.data[0].title}</p>
                        <p><strong>来源:</strong> ${response.data.data[0].author}</p>
                        <p><strong>时间:</strong> ${response.data.data[0].date}</p>
                        <p><strong>图片:</strong> <img src="${response.data.data[0].image}" style="max-width: 200px;" /></p>
                    `;
                } else {
                    resultDiv.innerHTML = '❌ API返回数据格式错误';
                }
                
            } catch (error) {
                console.error('API调用失败:', error);
                resultDiv.innerHTML = `❌ API调用失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>
