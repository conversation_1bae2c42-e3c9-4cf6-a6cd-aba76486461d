# 白敬亭个人信息网站

一个现代化的明星个人信息展示网站，专门为白敬亭打造，包含个人简介、音乐作品、影视作品和实时娱乐新闻。

## 🌟 功能特色

### 📱 页面功能
- **明星简介** - 详细的个人信息、成就展示和轮播图
- **音乐作品** - 专辑展示和在线音乐播放器
- **影视作品** - 电影、电视剧、音乐录影带分类展示
- **娱乐新闻** - 实时获取白敬亭相关新闻资讯

### 🎨 设计特色
- **响应式设计** - 完美适配桌面端和移动端
- **现代化UI** - 使用Tailwind CSS构建美观界面
- **流畅动画** - 悬停效果、淡入动画等交互体验
- **渐变主题** - 紫色到蓝色的现代渐变配色

### 🎵 交互功能
- **轮播图** - 自动播放、手动切换、指示器导航
- **音乐播放器** - 播放/暂停、上一首/下一首、进度控制、音量调节
- **新闻筛选** - 按分类筛选新闻内容
- **模态框** - 新闻详情和电影详情弹窗

## 🛠️ 技术栈

- **前端框架**: Vue 2
- **路由管理**: Vue Router
- **样式框架**: Tailwind CSS (CDN)
- **图标库**: Font Awesome
- **HTTP客户端**: Axios
- **后端代理**: Express.js + CORS
- **图片服务**: Unsplash API

## 🚀 快速开始

### 环境要求
- Node.js 14+
- npm 6+

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
# 同时启动前端和后端服务器
npm run dev

# 或者分别启动
npm run server  # 启动API代理服务器 (端口3001)
npm run serve   # 启动前端开发服务器 (端口8082)
```

### 访问应用
- 前端应用: http://localhost:8082
- API代理服务器: http://localhost:3001
