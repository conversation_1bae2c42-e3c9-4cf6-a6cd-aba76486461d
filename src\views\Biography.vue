<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 轮播图 -->
    <Carousel :images="starInfo.carouselImages" />

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <!-- 明星基本信息 -->
      <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
        <div class="md:flex">
          <!-- 头像 -->
          <div class="md:w-1/3 p-8">
            <div class="relative">
              <img
                :src="starInfo.avatar"
                :alt="starInfo.name"
                class="w-full max-w-sm mx-auto rounded-xl shadow-lg"
              />
              <div class="absolute -bottom-4 -right-4 bg-primary-500 text-white p-3 rounded-full">
                <i class="fas fa-star text-xl"></i>
              </div>
            </div>
          </div>

          <!-- 基本信息 -->
          <div class="md:w-2/3 p-8">
            <div class="mb-6">
              <h1 class="text-4xl font-bold text-gray-900 mb-2">{{ starInfo.name }}</h1>
              <h2 class="text-xl text-gray-600 mb-4">{{ starInfo.englishName }}</h2>
              <p class="text-lg text-primary-600 font-medium">{{ starInfo.profession }}</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-4">
                <div class="flex items-center">
                  <i class="fas fa-birthday-cake text-primary-500 w-6"></i>
                  <span class="ml-3 text-gray-700">{{ starInfo.birthDate }}</span>
                </div>
                <div class="flex items-center">
                  <i class="fas fa-map-marker-alt text-primary-500 w-6"></i>
                  <span class="ml-3 text-gray-700">{{ starInfo.birthPlace }}</span>
                </div>
                <div class="flex items-center">
                  <i class="fas fa-ruler-vertical text-primary-500 w-6"></i>
                  <span class="ml-3 text-gray-700">{{ starInfo.height }}</span>
                </div>
              </div>
              <div class="space-y-4">
                <div class="flex items-center">
                  <i class="fas fa-flag text-primary-500 w-6"></i>
                  <span class="ml-3 text-gray-700">{{ starInfo.nationality }}</span>
                </div>
                <div class="flex items-center">
                  <i class="fas fa-building text-primary-500 w-6"></i>
                  <span class="ml-3 text-gray-700">{{ starInfo.agency }}</span>
                </div>
              </div>
            </div>

            <!-- 社交媒体 -->
            <div class="mt-8">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">社交媒体</h3>
              <div class="flex space-x-4">
                <a
                  v-for="(handle, platform) in starInfo.socialMedia"
                  :key="platform"
                  href="#"
                  class="social-link"
                  :class="getSocialClass(platform)"
                >
                  <i :class="getSocialIcon(platform)" class="text-lg"></i>
                  <span class="ml-2">{{ handle }}</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 个人简介 -->
      <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
          <i class="fas fa-user-circle text-primary-500 mr-3"></i>
          个人简介
        </h2>
        <div class="prose prose-lg max-w-none text-gray-700 leading-relaxed">
          <div v-html="formattedBiography"></div>
        </div>
      </div>

      <!-- 主要成就 -->
      <div class="bg-white rounded-xl shadow-lg p-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
          <i class="fas fa-trophy text-primary-500 mr-3"></i>
          主要成就
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="(achievement, index) in starInfo.achievements"
            :key="index"
            class="achievement-card"
          >
            <div class="flex items-center">
              <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                <i class="fas fa-award text-primary-600 text-sm"></i>
              </div>
              <span class="text-gray-800 font-medium">{{ achievement }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Carousel from '@/components/Carousel.vue'
import { starInfo } from '@/data/starData.js'

export default {
  name: 'Biography',
  components: {
    Carousel
  },
  data() {
    return {
      starInfo
    }
  },
  computed: {
    formattedBiography() {
      return this.starInfo.biography
        .split('\n')
        .filter(paragraph => paragraph.trim())
        .map(paragraph => `<p class="mb-4">${paragraph.trim()}</p>`)
        .join('')
    }
  },
  methods: {
    getSocialIcon(platform) {
      const icons = {
        weibo: 'fab fa-weibo',
        douyin: 'fas fa-music',
        xiaohongshu: 'fas fa-book',
        instagram: 'fab fa-instagram'
      }
      return icons[platform] || 'fas fa-link'
    },
    getSocialClass(platform) {
      const classes = {
        weibo: 'bg-red-500 text-white',
        douyin: 'bg-black text-white',
        xiaohongshu: 'bg-pink-500 text-white',
        instagram: 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
      }
      return classes[platform] || 'bg-gray-500 text-white'
    }
  }
}
</script>

<style scoped>
.social-link {
  @apply inline-flex items-center px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:scale-105 hover:shadow-lg;
}

.achievement-card {
  @apply bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors duration-200;
}

.prose p {
  margin-bottom: 1rem;
  line-height: 1.7;
}
</style>
