// 图片资源路径配置
// 使用require动态导入图片，确保webpack能正确处理

// 明星相关图片
export const starImages = {
  // 头像
  avatar: require('./star/avatar.jpg'),
  
  // 轮播图
  carousel: [
    require('./star/carousel/carousel-1.jpg'),
    require('./star/carousel/carousel-2.jpg'),
    require('./star/carousel/carousel-3.jpg'),
    require('./star/carousel/carousel-4.jpg'),
    require('./star/carousel/carousel-5.jpg')
  ],
  
  // 社交媒体
  social: {
    profile: require('./star/social/profile.jpg')
  }
}

// 音乐相关图片
export const musicImages = {
  // 专辑封面
  albums: [
    require('./music/albums/album-1.jpg'),
    require('./music/albums/album-2.jpg'),
    require('./music/albums/album-3.jpg'),
    require('./music/albums/album-4.jpg')
  ],
  
  // 热门歌曲封面
  featured: [
    require('./music/featured/song-1.jpg'),
    require('./music/featured/song-2.jpg'),
    require('./music/featured/song-3.jpg')
  ]
}

// 影视相关图片
export const movieImages = {
  // 电影海报
  posters: [
    require('./movies/posters/movie-1.jpg'),
    require('./movies/posters/movie-2.jpg'),
    require('./movies/posters/movie-3.jpg'),
    require('./movies/posters/movie-4.jpg')
  ],
  
  // 音乐录影带缩略图
  mv: [
    require('./movies/mv/mv-1.jpg'),
    require('./movies/mv/mv-2.jpg'),
    require('./movies/mv/mv-3.jpg'),
    require('./movies/mv/mv-4.jpg')
  ],
  
  // 电视节目图片
  tv: [
    require('./movies/tv/tv-1.jpg'),
    require('./movies/tv/tv-2.jpg'),
    require('./movies/tv/tv-3.jpg')
  ]
}

// 新闻相关图片
export const newsImages = {
  // 默认新闻图片
  default: [
    require('./news/default/news-1.jpg'),
    require('./news/default/news-2.jpg'),
    require('./news/default/news-3.jpg'),
    require('./news/default/news-4.jpg')
  ],
  
  // 头条新闻图片
  featured: [
    require('./news/featured/featured-1.jpg')
  ]
}

// 通用图片
export const commonImages = {
  placeholder: require('./common/placeholder.jpg'),
  loading: require('./common/loading.gif'),
  error: require('./common/error.jpg')
}

// 获取随机默认新闻图片
export function getRandomNewsImage() {
  const randomIndex = Math.floor(Math.random() * newsImages.default.length)
  return newsImages.default[randomIndex]
}

// 获取专辑封面（按索引）
export function getAlbumCover(index) {
  return musicImages.albums[index % musicImages.albums.length]
}

// 获取电影海报（按索引）
export function getMoviePoster(index) {
  return movieImages.posters[index % movieImages.posters.length]
}

// 获取MV缩略图（按索引）
export function getMVThumbnail(index) {
  return movieImages.mv[index % movieImages.mv.length]
}

// 获取电视节目图片（按索引）
export function getTVImage(index) {
  return movieImages.tv[index % movieImages.tv.length]
}

// 图片预加载函数
export function preloadImages() {
  const allImages = [
    ...Object.values(starImages.carousel),
    ...musicImages.albums,
    ...musicImages.featured,
    ...movieImages.posters,
    ...movieImages.mv,
    ...movieImages.tv,
    ...newsImages.default,
    ...newsImages.featured,
    starImages.avatar,
    starImages.social.profile,
    commonImages.placeholder,
    commonImages.error
  ]
  
  allImages.forEach(src => {
    const img = new Image()
    img.src = src
  })
}
