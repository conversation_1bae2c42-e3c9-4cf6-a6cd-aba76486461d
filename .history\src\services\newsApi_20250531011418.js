import axios from 'axios'
import { getRandomNewsImage } from '@/assets/images/index.js'

// 新闻API配置
const NEWS_API_CONFIG = {
  baseURL: 'http://localhost:3001/api/news',
  defaultParams: {
    num: '15',
    page: '1',
    word: '白敬亭'
  }
}

// 创建axios实例
const newsApiClient = axios.create({
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
newsApiClient.interceptors.request.use(
  config => {
    console.log('发起新闻API请求:', config.url)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
newsApiClient.interceptors.response.use(
  response => {
    if (response.data && response.data.success) {
      return response.data
    } else {
      throw new Error(response.data?.error || '请求失败')
    }
  },
  error => {
    console.error('响应错误:', error)
    // 如果API请求失败，返回模拟数据
    return Promise.resolve(getMockNewsData())
  }
)

// 获取白敬亭相关新闻
export const getBaiJingtingNews = async (params = {}) => {
  try {
    const requestParams = {
      ...NEWS_API_CONFIG.defaultParams,
      ...params
    }

    const response = await newsApiClient.get(NEWS_API_CONFIG.baseURL, {
      params: requestParams
    })

    // 代理服务器已经处理了数据转换
    if (response.data.success && response.data.data) {
      // 确保图片URL正确处理，直接使用API返回的图片
      const processedData = response.data.data.map(item => ({
        ...item,
        // 直接使用API返回的图片URL，不使用本地图片
        image: item.image || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=400&fit=crop' // 只有在API没有返回图片时才使用默认图片
      }))

      return {
        success: true,
        data: processedData,
        total: response.data.total || processedData.length
      }
    }

    return { success: false, data: [] }
  } catch (error) {
    console.error('获取新闻失败:', error)
    // 返回模拟数据作为备选
    return getMockNewsData()
  }
}

// 这些辅助函数已经在server.js中实现，这里保留作为备用

// 模拟数据（作为API失败时的备选）
function getMockNewsData() {
  return {
    success: true,
    data: [
      {
        id: 'mock-1',
        title: '白敬亭：从春晚争议到央视加冕，破茧成蝶的演艺之路',
        summary: '白敬亭在演艺道路上经历了起伏，从争议到获得认可，展现了其坚韧的品格。',
        content: '白敬亭在演艺道路上经历了起伏，从争议到获得认可，展现了其坚韧的品格...',
        date: '2025-02-08',
        category: '娱乐',
        image: 'https://nimg.ws.126.net/?url=http%3A%2F%2Fbjnewsrec-cv.ws.126.net%2Flittle71604abc502j00srbhxf008rd000dj00idg.jpg&thumbnail=130y90&quality=100&type=jpg',
        author: '网易明星',
        tags: ['白敬亭', '春晚', '央视', '演艺'],
        url: 'https://www.163.com/dy/article/JNRA25MV05389WHM.html'
      },
      {
        id: 'mock-2',
        title: '白敬亭新剧《开端》收视率破纪录',
        summary: '白敬亭主演的悬疑剧《开端》播出后收视率持续攀升，获得观众一致好评。',
        content: '白敬亭主演的悬疑剧《开端》自播出以来就备受关注...',
        date: '2024-01-15',
        category: '电视剧',
        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=400&fit=crop',
        author: '娱乐记者',
        tags: ['白敬亭', '开端', '电视剧', '收视率'],
        url: '#'
      }
    ],
    total: 2
  }
}
