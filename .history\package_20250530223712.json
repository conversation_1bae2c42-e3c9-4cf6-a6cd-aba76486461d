{"name": "start-message", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"autoprefixer": "^10.4.21", "core-js": "^3.8.3", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "vue": "^2.6.14", "vue-router": "^3.6.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"vue/multi-word-component-names": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}