<template>
  <div class="relative w-full h-96 md:h-[500px] overflow-hidden rounded-xl shadow-2xl">
    <!-- 轮播图片 -->
    <div 
      class="flex transition-transform duration-500 ease-in-out h-full"
      :style="{ transform: `translateX(-${currentIndex * 100}%)` }"
    >
      <div
        v-for="(image, index) in images"
        :key="index"
        class="w-full h-full flex-shrink-0 relative"
      >
        <img
          :src="image.url"
          :alt="image.caption"
          class="w-full h-full object-cover"
          @load="onImageLoad"
        />
        <!-- 图片遮罩和标题 -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent">
          <div class="absolute bottom-6 left-6 right-6">
            <h3 class="text-white text-xl md:text-2xl font-bold mb-2 animate-fade-in">
              {{ image.caption }}
            </h3>
            <div class="flex items-center text-white/80 text-sm">
              <i class="fas fa-camera mr-2"></i>
              <span>{{ index + 1 }} / {{ images.length }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 导航按钮 -->
    <button
      @click="prevSlide"
      class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all duration-200 backdrop-blur-sm"
      :disabled="currentIndex === 0"
    >
      <i class="fas fa-chevron-left"></i>
    </button>
    
    <button
      @click="nextSlide"
      class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all duration-200 backdrop-blur-sm"
      :disabled="currentIndex === images.length - 1"
    >
      <i class="fas fa-chevron-right"></i>
    </button>

    <!-- 指示器 -->
    <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
      <button
        v-for="(image, index) in images"
        :key="index"
        @click="goToSlide(index)"
        class="w-3 h-3 rounded-full transition-all duration-200"
        :class="currentIndex === index ? 'bg-white' : 'bg-white/50 hover:bg-white/70'"
      ></button>
    </div>

    <!-- 播放/暂停按钮 -->
    <button
      @click="toggleAutoplay"
      class="absolute top-4 right-4 bg-white/20 hover:bg-white/30 text-white p-2 rounded-full transition-all duration-200 backdrop-blur-sm"
    >
      <i :class="isPlaying ? 'fas fa-pause' : 'fas fa-play'"></i>
    </button>
  </div>
</template>

<script>
export default {
  name: 'Carousel',
  props: {
    images: {
      type: Array,
      required: true,
      default: () => []
    },
    autoplay: {
      type: Boolean,
      default: true
    },
    interval: {
      type: Number,
      default: 5000
    }
  },
  data() {
    return {
      currentIndex: 0,
      isPlaying: this.autoplay,
      autoplayTimer: null,
      imagesLoaded: 0
    }
  },
  mounted() {
    if (this.isPlaying) {
      this.startAutoplay()
    }
  },
  beforeDestroy() {
    this.stopAutoplay()
  },
  methods: {
    nextSlide() {
      if (this.currentIndex < this.images.length - 1) {
        this.currentIndex++
      } else {
        this.currentIndex = 0
      }
    },
    prevSlide() {
      if (this.currentIndex > 0) {
        this.currentIndex--
      } else {
        this.currentIndex = this.images.length - 1
      }
    },
    goToSlide(index) {
      this.currentIndex = index
    },
    startAutoplay() {
      this.stopAutoplay()
      this.autoplayTimer = setInterval(() => {
        this.nextSlide()
      }, this.interval)
    },
    stopAutoplay() {
      if (this.autoplayTimer) {
        clearInterval(this.autoplayTimer)
        this.autoplayTimer = null
      }
    },
    toggleAutoplay() {
      this.isPlaying = !this.isPlaying
      if (this.isPlaying) {
        this.startAutoplay()
      } else {
        this.stopAutoplay()
      }
    },
    onImageLoad() {
      this.imagesLoaded++
    }
  },
  watch: {
    isPlaying(newVal) {
      if (newVal) {
        this.startAutoplay()
      } else {
        this.stopAutoplay()
      }
    }
  }
}
</script>

<style scoped>
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

button:disabled:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
}
</style>
