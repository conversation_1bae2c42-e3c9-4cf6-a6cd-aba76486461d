<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">娱乐新闻</h1>
        <p class="text-xl text-gray-600">最新资讯与动态</p>
      </div>

      <!-- 分类筛选 -->
      <div class="flex flex-wrap justify-center gap-2 mb-8">
        <button
          v-for="category in newsCategories"
          :key="category.id"
          @click="selectedCategory = category.id"
          class="category-filter"
          :class="{ 'category-filter-active': selectedCategory === category.id }"
        >
          <i :class="category.icon" class="mr-2"></i>
          {{ category.name }}
        </button>
      </div>

      <!-- 新闻列表 -->
      <div class="space-y-8">
        <!-- 头条新闻 -->
        <div v-if="featuredNews" class="featured-news" @click="selectedNews = featuredNews">
          <div class="relative">
            <img
              :src="featuredNews.image"
              :alt="featuredNews.title"
              class="w-full h-64 md:h-80 object-cover rounded-xl"
            />
            <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent rounded-xl">
              <div class="absolute bottom-6 left-6 right-6">
                <div class="flex items-center space-x-2 mb-3">
                  <span class="bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                    头条
                  </span>
                  <span class="bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                    {{ featuredNews.category }}
                  </span>
                </div>
                <h2 class="text-2xl md:text-3xl font-bold text-white mb-2">{{ featuredNews.title }}</h2>
                <p class="text-gray-200 text-sm md:text-base">{{ featuredNews.summary }}</p>
                <div class="flex items-center text-gray-300 text-sm mt-3">
                  <i class="fas fa-calendar mr-2"></i>
                  <span>{{ featuredNews.date }}</span>
                  <i class="fas fa-user ml-4 mr-2"></i>
                  <span>{{ featuredNews.author }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 普通新闻 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="article in filteredNews"
            :key="article.id"
            class="news-card"
            @click="selectedNews = article"
          >
            <div class="relative">
              <img
                :src="article.image"
                :alt="article.title"
                class="w-full h-48 object-cover rounded-lg"
              />
              <div class="absolute top-2 left-2">
                <span class="bg-primary-600 text-white px-2 py-1 rounded text-xs font-medium">
                  {{ article.category }}
                </span>
              </div>
            </div>
            <div class="p-4">
              <h3 class="font-bold text-gray-900 mb-2 line-clamp-2">{{ article.title }}</h3>
              <p class="text-gray-600 text-sm mb-3 line-clamp-3">{{ article.summary }}</p>
              <div class="flex items-center justify-between text-xs text-gray-500">
                <span>{{ article.date }}</span>
                <span>{{ article.author }}</span>
              </div>
              <div class="flex flex-wrap gap-1 mt-2">
                <span
                  v-for="tag in article.tags.slice(0, 3)"
                  :key="tag"
                  class="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs"
                >
                  #{{ tag }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新闻详情模态框 -->
    <div
      v-if="selectedNews"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      @click="selectedNews = null"
    >
      <div
        class="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        @click.stop
      >
        <div class="relative">
          <img
            :src="selectedNews.image"
            :alt="selectedNews.title"
            class="w-full h-64 object-cover"
          />
          <button
            @click="selectedNews = null"
            class="absolute top-4 right-4 bg-black bg-opacity-50 text-white w-10 h-10 rounded-full flex items-center justify-center hover:bg-opacity-70 transition-colors"
          >
            <i class="fas fa-times"></i>
          </button>
          <div class="absolute bottom-4 left-4">
            <span class="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium">
              {{ selectedNews.category }}
            </span>
          </div>
        </div>
        <div class="p-8">
          <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ selectedNews.title }}</h1>
          <div class="flex items-center text-gray-600 text-sm mb-6">
            <i class="fas fa-calendar mr-2"></i>
            <span class="mr-4">{{ selectedNews.date }}</span>
            <i class="fas fa-user mr-2"></i>
            <span>{{ selectedNews.author }}</span>
          </div>
          <div class="prose prose-lg max-w-none text-gray-700 leading-relaxed">
            <div v-html="formattedContent"></div>
          </div>
          <div class="flex flex-wrap gap-2 mt-6 pt-6 border-t">
            <span
              v-for="tag in selectedNews.tags"
              :key="tag"
              class="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm"
            >
              #{{ tag }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { newsCategories } from '@/data/newsData.js'
import { getBaiJingtingNews } from '@/services/newsApi.js'

export default {
  name: 'News',
  data() {
    return {
      news: [],
      newsCategories,
      selectedCategory: 'all',
      selectedNews: null,
      loading: false,
      error: null
    }
  },
  computed: {
    featuredNews() {
      return this.news[0] // 第一条新闻作为头条
    },
    filteredNews() {
      const regularNews = this.news.slice(1) // 除了头条的其他新闻
      if (this.selectedCategory === 'all') {
        return regularNews
      }
      return regularNews.filter(article => article.category === this.selectedCategory)
    },
    formattedContent() {
      if (!this.selectedNews) return ''
      return this.selectedNews.content
        .split('\n')
        .filter(paragraph => paragraph.trim())
        .map(paragraph => `<p class="mb-4">${paragraph.trim()}</p>`)
        .join('')
    }
  },
  async mounted() {
    await this.loadNews()
  },
  methods: {
    async loadNews() {
      this.loading = true
      this.error = null

      try {
        const response = await getBaiJingtingNews({
          num: '15',
          page: '1'
        })

        if (response.success && response.data) {
          this.news = response.data
        } else {
          this.error = '获取新闻失败'
        }
      } catch (error) {
        console.error('加载新闻失败:', error)
        this.error = '网络连接失败，请稍后重试'
      } finally {
        this.loading = false
      }
    },

    async refreshNews() {
      await this.loadNews()
    }
  }
}
</script>

<style scoped>
.category-filter {
  @apply px-4 py-2 rounded-full font-medium transition-all duration-200 text-gray-600 bg-white hover:text-primary-600 hover:bg-primary-50 border border-gray-200;
}

.category-filter-active {
  @apply bg-primary-500 text-white border-primary-500 shadow-lg;
}

.featured-news {
  @apply cursor-pointer transition-all duration-300 hover:scale-[1.02] hover:shadow-xl;
}

.news-card {
  @apply bg-white rounded-xl shadow-lg overflow-hidden cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.prose p {
  margin-bottom: 1rem;
  line-height: 1.7;
}
</style>
