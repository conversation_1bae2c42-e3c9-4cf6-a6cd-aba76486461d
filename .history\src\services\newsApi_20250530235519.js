import axios from 'axios'

// 新闻API配置
const NEWS_API_CONFIG = {
  baseURL: 'http://localhost:3001/api/news',
  defaultParams: {
    num: '15',
    page: '1',
    word: '白敬亭'
  }
}

// 创建axios实例
const newsApiClient = axios.create({
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
newsApiClient.interceptors.request.use(
  config => {
    console.log('发起新闻API请求:', config.url)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
newsApiClient.interceptors.response.use(
  response => {
    if (response.data && response.data.success) {
      return response.data
    } else {
      throw new Error(response.data?.error || '请求失败')
    }
  },
  error => {
    console.error('响应错误:', error)
    // 如果API请求失败，返回模拟数据
    return Promise.resolve(getMockNewsData())
  }
)

// 获取白敬亭相关新闻
export const getBaiJingtingNews = async (params = {}) => {
  try {
    const requestParams = {
      key: NEWS_API_CONFIG.apiKey,
      ...NEWS_API_CONFIG.defaultParams,
      ...params
    }

    const response = await newsApiClient.get(NEWS_API_CONFIG.baseURL, {
      params: requestParams
    })

    // 转换API数据格式为我们的数据格式
    if (response.result && response.result.newslist) {
      return {
        success: true,
        data: response.result.newslist.map(item => ({
          id: item.id,
          title: item.title,
          summary: item.description || extractSummary(item.title),
          content: generateContent(item.title, item.description),
          date: formatDate(item.ctime),
          category: extractCategory(item.title),
          image: item.picUrl || getDefaultImage(),
          author: item.source || '娱乐记者',
          tags: extractTags(item.title),
          url: item.url
        }))
      }
    }

    return { success: false, data: [] }
  } catch (error) {
    console.error('获取新闻失败:', error)
    // 返回模拟数据作为备选
    return getMockNewsData()
  }
}

// 辅助函数：提取摘要
function extractSummary(title) {
  if (title.length > 50) {
    return title.substring(0, 50) + '...'
  }
  return title + '，更多详情请关注最新报道。'
}

// 辅助函数：生成内容
function generateContent(title, description) {
  const baseContent = `
    ${title}

    ${description || '这是一条关于白敬亭的最新娱乐新闻。'}

    白敬亭作为新生代演员，一直备受关注。他的每一个动态都牵动着粉丝们的心。从《最好的我们》中的余淮，到《开端》中的肖鹤云，白敬亭用自己的演技征服了观众。

    近年来，白敬亭不断挑战不同类型的角色，展现了作为演员的专业素养和敬业精神。无论是青春校园剧还是悬疑推理剧，他都能很好地诠释角色，给观众留下深刻印象。

    除了演戏，白敬亭在综艺节目中也展现了真实可爱的一面，幽默风趣的性格让他收获了更多粉丝的喜爱。
  `
  return baseContent.trim()
}

// 辅助函数：格式化日期
function formatDate(dateString) {
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).replace(/\//g, '-')
  } catch {
    return new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')
  }
}

// 辅助函数：提取分类
function extractCategory(title) {
  if (title.includes('电影') || title.includes('影视')) return '电影'
  if (title.includes('音乐') || title.includes('歌曲')) return '音乐'
  if (title.includes('综艺') || title.includes('节目')) return '综艺'
  if (title.includes('恋情') || title.includes('感情')) return '感情'
  if (title.includes('获奖') || title.includes('奖项')) return '荣誉'
  if (title.includes('慈善') || title.includes('公益')) return '慈善'
  return '娱乐'
}

// 辅助函数：提取标签
function extractTags(title) {
  const tags = []
  if (title.includes('白敬亭')) tags.push('白敬亭')
  if (title.includes('演员')) tags.push('演员')
  if (title.includes('电视剧')) tags.push('电视剧')
  if (title.includes('电影')) tags.push('电影')
  if (title.includes('综艺')) tags.push('综艺')
  if (title.includes('恋情')) tags.push('恋情')
  if (title.includes('获奖')) tags.push('获奖')

  // 如果没有提取到标签，添加默认标签
  if (tags.length === 0) {
    tags.push('娱乐新闻', '明星动态')
  }

  return tags
}

// 辅助函数：获取默认图片
function getDefaultImage() {
  const defaultImages = [
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=400&fit=crop',
    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=600&h=400&fit=crop',
    'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=600&h=400&fit=crop',
    'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=600&h=400&fit=crop'
  ]
  return defaultImages[Math.floor(Math.random() * defaultImages.length)]
}

// 模拟数据（作为API失败时的备选）
function getMockNewsData() {
  return {
    success: true,
    data: [
      {
        id: '1',
        title: '白敬亭新剧《开端》收视率破纪录',
        summary: '白敬亭主演的悬疑剧《开端》播出后收视率持续攀升，获得观众一致好评。',
        content: '白敬亭主演的悬疑剧《开端》自播出以来就备受关注...',
        date: '2024-01-15',
        category: '电视剧',
        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=400&fit=crop',
        author: '娱乐记者',
        tags: ['白敬亭', '开端', '电视剧', '收视率']
      }
    ]
  }
}
