# 公共图片资源目录

这个目录用于存放可以通过URL直接访问的图片资源。

## 目录结构

```
public/images/
└── news/
    ├── default/
    │   ├── news-1.jpg    # 默认新闻图片1 (600x400)
    │   ├── news-2.jpg    # 默认新闻图片2 (600x400)
    │   ├── news-3.jpg    # 默认新闻图片3 (600x400)
    │   └── news-4.jpg    # 默认新闻图片4 (600x400)
    └── featured/
        └── featured-1.jpg # 头条新闻图片 (800x500)
```

## 使用说明

1. 将对应的图片文件放入相应的目录
2. 确保文件名与上述结构一致
3. 这些图片将通过以下URL访问：
   - `/images/news/default/news-1.jpg`
   - `/images/news/default/news-2.jpg`
   - 等等...

## 图片规格

- **默认新闻图片**: 600x400px, JPG格式, < 200KB
- **头条新闻图片**: 800x500px, JPG格式, < 250KB

## 注意事项

- 这些图片主要用于新闻API返回的默认图片
- 当API返回的图片无法加载时，会使用这些默认图片
- 建议使用压缩工具优化图片大小
