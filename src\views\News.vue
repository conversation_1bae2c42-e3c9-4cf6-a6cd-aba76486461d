<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">娱乐新闻</h1>
        <p class="text-xl text-gray-600">白敬亭最新资讯与动态</p>
        <button
          @click="refreshNews"
          :disabled="loading"
          class="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 disabled:opacity-50"
        >
          <i class="fas fa-sync-alt mr-2" :class="{ 'animate-spin': loading }"></i>
          {{ loading ? '加载中...' : '刷新新闻' }}
        </button>
      </div>

      <!-- 分类筛选 -->
      <div class="flex flex-wrap justify-center gap-2 mb-8">
        <button
          v-for="category in newsCategories"
          :key="category.id"
          @click="selectedCategory = category.id"
          class="category-filter"
          :class="{ 'category-filter-active': selectedCategory === category.id }"
        >
          <i :class="category.icon" class="mr-2"></i>
          {{ category.name }}
        </button>
      </div>

      <!-- 错误状态 -->
      <div v-if="error" class="text-center py-12">
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <i class="fas fa-exclamation-triangle text-red-500 text-3xl mb-4"></i>
          <h3 class="text-lg font-semibold text-red-800 mb-2">加载失败</h3>
          <p class="text-red-600 mb-4">{{ error }}</p>
          <button
            @click="refreshNews"
            class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"
          >
            重试
          </button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-else-if="loading" class="text-center py-12">
        <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        <p class="mt-4 text-gray-600">正在加载最新新闻...</p>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!news.length" class="text-center py-12">
        <i class="fas fa-newspaper text-gray-400 text-6xl mb-4"></i>
        <h3 class="text-xl font-semibold text-gray-600 mb-2">暂无新闻</h3>
        <p class="text-gray-500">请稍后再试或点击刷新按钮</p>
      </div>

      <!-- 新闻列表 -->
      <div v-else class="space-y-8">
        <!-- 头条新闻 -->
        <div v-if="featuredNews" class="featured-news" @click="selectedNews = featuredNews">
          <div class="relative">
            <img
              :src="featuredNews.image"
              :alt="featuredNews.title"
              class="w-full h-64 md:h-80 object-cover rounded-xl"
            />
            <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent rounded-xl">
              <div class="absolute bottom-6 left-6 right-6">
                <div class="flex items-center space-x-2 mb-3">
                  <span class="bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                    头条
                  </span>
                  <span class="bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                    {{ featuredNews.category }}
                  </span>
                </div>
                <h2 class="text-2xl md:text-3xl font-bold text-white mb-2">{{ featuredNews.title }}</h2>
                <p class="text-gray-200 text-sm md:text-base">{{ featuredNews.summary }}</p>
                <div class="flex items-center text-gray-300 text-sm mt-3">
                  <i class="fas fa-calendar mr-2"></i>
                  <span>{{ featuredNews.date }}</span>
                  <i class="fas fa-user ml-4 mr-2"></i>
                  <span>{{ featuredNews.author }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 普通新闻 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="article in filteredNews"
            :key="article.id"
            class="news-card"
            @click="selectedNews = article"
          >
            <div class="relative">
              <img
                :src="article.image"
                :alt="article.title"
                class="w-full h-48 object-cover rounded-lg"
              />
              <div class="absolute top-2 left-2">
                <span class="bg-primary-600 text-white px-2 py-1 rounded text-xs font-medium">
                  {{ article.category }}
                </span>
              </div>
            </div>
            <div class="p-4">
              <h3 class="font-bold text-gray-900 mb-2 line-clamp-2">{{ article.title }}</h3>
              <p class="text-gray-600 text-sm mb-3 line-clamp-3">{{ article.summary }}</p>
              <div class="flex items-center justify-between text-xs text-gray-500">
                <span>{{ article.date }}</span>
                <span>{{ article.author }}</span>
              </div>
              <div class="flex flex-wrap gap-1 mt-2">
                <span
                  v-for="tag in article.tags.slice(0, 3)"
                  :key="tag"
                  class="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs"
                >
                  #{{ tag }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页组件 -->
        <div v-if="!loading && !error && news.length > 0" class="mt-12 flex justify-center">
          <div class="flex items-center space-x-2">
            <!-- 上一页 -->
            <button
              @click="goToPage(currentPage - 1)"
              :disabled="currentPage <= 1"
              class="pagination-btn"
              :class="{ 'pagination-btn-disabled': currentPage <= 1 }"
            >
              <i class="fas fa-chevron-left"></i>
            </button>

            <!-- 页码 -->
            <template v-for="(page, index) in getPageNumbers()">
              <button
                v-if="page !== '...'"
                :key="`page-${index}`"
                @click="goToPage(page)"
                class="pagination-btn"
                :class="{ 'pagination-btn-active': page === currentPage }"
              >
                {{ page }}
              </button>
              <span v-else :key="`dots-${index}`" class="px-2 text-gray-500">...</span>
            </template>

            <!-- 下一页 -->
            <button
              @click="goToPage(currentPage + 1)"
              :disabled="currentPage >= totalPages"
              class="pagination-btn"
              :class="{ 'pagination-btn-disabled': currentPage >= totalPages }"
            >
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>

        <!-- 加载更多按钮 -->
        <div v-if="!loading && !error && news.length > 0 && currentPage < totalPages" class="mt-8 text-center">
          <button
            @click="loadMoreNews"
            class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors duration-200"
          >
            <i class="fas fa-plus mr-2"></i>
            加载更多新闻
          </button>
        </div>
      </div>
    </div>

    <!-- 新闻详情模态框 -->
    <div
      v-if="selectedNews"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      @click="selectedNews = null"
    >
      <div
        class="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        @click.stop
      >
        <div class="relative">
          <img
            :src="selectedNews.image"
            :alt="selectedNews.title"
            class="w-full h-64 object-cover"
          />
          <button
            @click="selectedNews = null"
            class="absolute top-4 right-4 bg-black bg-opacity-50 text-white w-10 h-10 rounded-full flex items-center justify-center hover:bg-opacity-70 transition-colors"
          >
            <i class="fas fa-times"></i>
          </button>
          <div class="absolute bottom-4 left-4">
            <span class="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium">
              {{ selectedNews.category }}
            </span>
          </div>
        </div>
        <div class="p-8">
          <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ selectedNews.title }}</h1>
          <div class="flex items-center text-gray-600 text-sm mb-6">
            <i class="fas fa-calendar mr-2"></i>
            <span class="mr-4">{{ selectedNews.date }}</span>
            <i class="fas fa-user mr-2"></i>
            <span>{{ selectedNews.author }}</span>
          </div>
          <div class="prose prose-lg max-w-none text-gray-700 leading-relaxed">
            <div v-html="formattedContent"></div>
          </div>
          <div class="flex flex-wrap gap-2 mt-6 pt-6 border-t">
            <span
              v-for="tag in selectedNews.tags"
              :key="tag"
              class="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm"
            >
              #{{ tag }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { newsCategories } from '@/data/newsData.js'
import { getBaiJingtingNews } from '@/services/newsApi.js'

export default {
  name: 'News',
  data() {
    return {
      news: [],
      newsCategories,
      selectedCategory: 'all',
      selectedNews: null,
      loading: false,
      error: null,
      currentPage: 1,
      totalPages: 1,
      pageSize: 10
    }
  },
  computed: {
    featuredNews() {
      return this.news[0] // 第一条新闻作为头条
    },
    filteredNews() {
      const regularNews = this.news.slice(1) // 除了头条的其他新闻
      if (this.selectedCategory === 'all') {
        return regularNews
      }
      return regularNews.filter(article => article.category === this.selectedCategory)
    },
    formattedContent() {
      if (!this.selectedNews) return ''
      return this.selectedNews.content
        .split('\n')
        .filter(paragraph => paragraph.trim())
        .map(paragraph => `<p class="mb-4">${paragraph.trim()}</p>`)
        .join('')
    }
  },
  async mounted() {
    await this.loadNews()
  },
  methods: {
    async loadNews(page = 1) {
      this.loading = true
      this.error = null

      try {
        console.log('开始加载新闻，页码:', page)
        const response = await getBaiJingtingNews({
          num: this.pageSize.toString(),
          page: page.toString()
        })

        console.log('API响应:', response)

        if (response.success && response.data && response.data.length > 0) {
          if (page === 1) {
            this.news = response.data
          } else {
            this.news = [...this.news, ...response.data]
          }
          this.currentPage = page
          // 假设总页数，实际应该从API返回
          this.totalPages = Math.max(5, page + 1)
          console.log('新闻加载成功，数量:', response.data.length)
        } else {
          console.error('API响应格式错误:', response)
          this.error = '获取新闻失败'
        }
      } catch (error) {
        console.error('加载新闻失败:', error)
        this.error = '网络连接失败，请稍后重试'
      } finally {
        this.loading = false
      }
    },

    async refreshNews() {
      this.currentPage = 1
      await this.loadNews(1)
    },

    async loadMoreNews() {
      if (this.currentPage < this.totalPages && !this.loading) {
        await this.loadNews(this.currentPage + 1)
      }
    },

    async goToPage(page) {
      if (page !== this.currentPage && page >= 1 && page <= this.totalPages) {
        this.currentPage = page
        this.news = []
        await this.loadNews(page)
      }
    },

    getPageNumbers() {
      const pages = []
      const current = this.currentPage
      const total = this.totalPages

      if (total <= 7) {
        // 如果总页数小于等于7，显示所有页码
        for (let i = 1; i <= total; i++) {
          pages.push(i)
        }
      } else {
        // 总是显示第一页
        pages.push(1)

        if (current > 4) {
          pages.push('...')
        }

        // 显示当前页附近的页码
        const start = Math.max(2, current - 1)
        const end = Math.min(total - 1, current + 1)

        for (let i = start; i <= end; i++) {
          pages.push(i)
        }

        if (current < total - 3) {
          pages.push('...')
        }

        // 总是显示最后一页
        if (total > 1) {
          pages.push(total)
        }
      }

      return pages
    }
  }
}
</script>

<style scoped>
.category-filter {
  @apply px-4 py-2 rounded-full font-medium transition-all duration-200 text-gray-600 bg-white hover:text-primary-600 hover:bg-primary-50 border border-gray-200;
}

.category-filter-active {
  @apply bg-primary-500 text-white border-primary-500 shadow-lg;
}

.featured-news {
  @apply cursor-pointer transition-all duration-300 hover:scale-[1.02] hover:shadow-xl;
}

.news-card {
  @apply bg-white rounded-xl shadow-lg overflow-hidden cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.prose p {
  margin-bottom: 1rem;
  line-height: 1.7;
}

.pagination-btn {
  @apply w-10 h-10 flex items-center justify-center rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200;
}

.pagination-btn-active {
  @apply bg-blue-500 text-white border-blue-500 hover:bg-blue-600;
}

.pagination-btn-disabled {
  @apply opacity-50 cursor-not-allowed hover:bg-white hover:border-gray-300;
}
</style>
