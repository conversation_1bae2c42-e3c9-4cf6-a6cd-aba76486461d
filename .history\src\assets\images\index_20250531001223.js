// 图片资源路径配置
// 当前使用网络图片作为占位，您可以将这些URL替换为本地图片路径

// 明星相关图片
export const starImages = {
  // 头像
  avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',

  // 轮播图
  carousel: [
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=1200&h=800&fit=crop',
    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=1200&h=800&fit=crop',
    'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=1200&h=800&fit=crop',
    'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=1200&h=800&fit=crop',
    'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=1200&h=800&fit=crop'
  ],

  // 社交媒体
  social: {
    profile: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop'
  }
}

// 音乐相关图片
export const musicImages = {
  // 专辑封面
  albums: [
    require('./music/albums/album-1.jpg'),
    require('./music/albums/album-2.jpg'),
    require('./music/albums/album-3.jpg'),
    require('./music/albums/album-4.jpg')
  ],

  // 热门歌曲封面
  featured: [
    require('./music/featured/song-1.jpg'),
    require('./music/featured/song-2.jpg'),
    require('./music/featured/song-3.jpg')
  ]
}

// 影视相关图片
export const movieImages = {
  // 电影海报
  posters: [
    require('./movies/posters/movie-1.jpg'),
    require('./movies/posters/movie-2.jpg'),
    require('./movies/posters/movie-3.jpg'),
    require('./movies/posters/movie-4.jpg')
  ],

  // 音乐录影带缩略图
  mv: [
    require('./movies/mv/mv-1.jpg'),
    require('./movies/mv/mv-2.jpg'),
    require('./movies/mv/mv-3.jpg'),
    require('./movies/mv/mv-4.jpg')
  ],

  // 电视节目图片
  tv: [
    require('./movies/tv/tv-1.jpg'),
    require('./movies/tv/tv-2.jpg'),
    require('./movies/tv/tv-3.jpg')
  ]
}

// 新闻相关图片
export const newsImages = {
  // 默认新闻图片
  default: [
    require('./news/default/news-1.jpg'),
    require('./news/default/news-2.jpg'),
    require('./news/default/news-3.jpg'),
    require('./news/default/news-4.jpg')
  ],

  // 头条新闻图片
  featured: [
    require('./news/featured/featured-1.jpg')
  ]
}

// 通用图片
export const commonImages = {
  placeholder: require('./common/placeholder.jpg'),
  loading: require('./common/loading.gif'),
  error: require('./common/error.jpg')
}

// 获取随机默认新闻图片
export function getRandomNewsImage() {
  const randomIndex = Math.floor(Math.random() * newsImages.default.length)
  return newsImages.default[randomIndex]
}

// 获取专辑封面（按索引）
export function getAlbumCover(index) {
  return musicImages.albums[index % musicImages.albums.length]
}

// 获取电影海报（按索引）
export function getMoviePoster(index) {
  return movieImages.posters[index % movieImages.posters.length]
}

// 获取MV缩略图（按索引）
export function getMVThumbnail(index) {
  return movieImages.mv[index % movieImages.mv.length]
}

// 获取电视节目图片（按索引）
export function getTVImage(index) {
  return movieImages.tv[index % movieImages.tv.length]
}

// 图片预加载函数
export function preloadImages() {
  const allImages = [
    ...Object.values(starImages.carousel),
    ...musicImages.albums,
    ...musicImages.featured,
    ...movieImages.posters,
    ...movieImages.mv,
    ...movieImages.tv,
    ...newsImages.default,
    ...newsImages.featured,
    starImages.avatar,
    starImages.social.profile,
    commonImages.placeholder,
    commonImages.error
  ]

  allImages.forEach(src => {
    const img = new Image()
    img.src = src
  })
}
