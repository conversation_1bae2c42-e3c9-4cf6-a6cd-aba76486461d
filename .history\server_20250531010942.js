const express = require('express');
const cors = require('cors');
const axios = require('axios');

const app = express();
const PORT = 3001;

// 启用CORS
app.use(cors());
app.use(express.json());

// 新闻API代理路由
app.get('/api/news', async (req, res) => {
  try {
    const { num = '15', page = '1', word = '白敬亭' } = req.query;

    const response = await axios.get('http://apis.juhe.cn/fapigx/huabian/query', {
      params: {
        key: '995996e75581f252c7627b6da94e343c',
        num,
        page,
        rand: '',
        word
      }
    });

    if (response.data && response.data.error_code === 0) {
      // 转换数据格式
      const newsData = response.data.result.newslist.map(item => ({
        id: item.id,
        title: item.title,
        summary: item.description || extractSummary(item.title),
        content: generateContent(item.title, item.description),
        date: formatDate(item.ctime),
        category: extractCategory(item.title),
        image: item.picUrl, // 直接使用API返回的图片URL
        author: item.source || '娱乐记者',
        tags: extractTags(item.title),
        url: item.url
      }));

      res.json({
        success: true,
        data: newsData
      });
    } else {
      res.status(400).json({
        success: false,
        error: response.data?.reason || '请求失败'
      });
    }
  } catch (error) {
    console.error('API请求失败:', error.message);
    res.status(500).json({
      success: false,
      error: '服务器错误'
    });
  }
});

// 辅助函数
function extractSummary(title) {
  if (title.length > 50) {
    return title.substring(0, 50) + '...'
  }
  return title + '，更多详情请关注最新报道。'
}

function generateContent(title, description) {
  const baseContent = `
    ${title}

    ${description || '这是一条关于白敬亭的最新娱乐新闻。'}

    白敬亭作为新生代演员，一直备受关注。他的每一个动态都牵动着粉丝们的心。从《最好的我们》中的余淮，到《开端》中的肖鹤云，白敬亭用自己的演技征服了观众。

    近年来，白敬亭不断挑战不同类型的角色，展现了作为演员的专业素养和敬业精神。无论是青春校园剧还是悬疑推理剧，他都能很好地诠释角色，给观众留下深刻印象。

    除了演戏，白敬亭在综艺节目中也展现了真实可爱的一面，幽默风趣的性格让他收获了更多粉丝的喜爱。
  `
  return baseContent.trim()
}

function formatDate(dateString) {
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).replace(/\//g, '-')
  } catch {
    return new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')
  }
}

function extractCategory(title) {
  if (title.includes('电影') || title.includes('影视')) return '电影'
  if (title.includes('音乐') || title.includes('歌曲')) return '音乐'
  if (title.includes('综艺') || title.includes('节目')) return '综艺'
  if (title.includes('恋情') || title.includes('感情')) return '感情'
  if (title.includes('获奖') || title.includes('奖项')) return '荣誉'
  if (title.includes('慈善') || title.includes('公益')) return '慈善'
  return '娱乐'
}

function extractTags(title) {
  const tags = []
  if (title.includes('白敬亭')) tags.push('白敬亭')
  if (title.includes('演员')) tags.push('演员')
  if (title.includes('电视剧')) tags.push('电视剧')
  if (title.includes('电影')) tags.push('电影')
  if (title.includes('综艺')) tags.push('综艺')
  if (title.includes('恋情')) tags.push('恋情')
  if (title.includes('获奖')) tags.push('获奖')

  if (tags.length === 0) {
    tags.push('娱乐新闻', '明星动态')
  }

  return tags
}

function getDefaultImage() {
  // 返回默认新闻图片（网络图片）
  // 只有在API没有返回图片时才使用
  const defaultImages = [
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=400&fit=crop',
    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=600&h=400&fit=crop',
    'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=600&h=400&fit=crop',
    'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=600&h=400&fit=crop'
  ]
  return defaultImages[Math.floor(Math.random() * defaultImages.length)]
}

app.listen(PORT, () => {
  console.log(`代理服务器运行在 http://localhost:${PORT}`);
});
